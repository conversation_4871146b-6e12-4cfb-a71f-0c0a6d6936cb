<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="上传小区" prop="uploadCommunity">
        <el-input
          v-model="queryParams.uploadCommunity"
          placeholder="请输入上传小区"  
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传人员" prop="uploadPersonnel">
        <el-input
          v-model="queryParams.uploadPersonnel"
          placeholder="请输入上传人员"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
             <el-form-item label="上传时间" prop="timeRange">
         <el-select v-model="queryParams.timeRange" placeholder="请选择时间范围" clearable>
           <el-option label="全部" value="all" />
           <el-option label="今天" value="day" />
           <el-option label="本月" value="month" />
           <el-option label="本季度" value="quarter" />
           <el-option label="今年" value="year" />
         </el-select>
       </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sc:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sc:info:remove']"
        >删除</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="id" width="80" />
      <el-table-column label="客厅" align="center" prop="livingRoom" width="150">
        <template slot-scope="scope">
          <div class="image-cell">
            <el-image 
              v-if="scope.row.livingRoom" 
              :src="formatImageUrl(scope.row.livingRoom)" 
              :preview-src-list="[formatImageUrl(scope.row.livingRoom)]"
              fit="cover"
              class="room-image"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>图片加载失败</span>
              </div>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无该图</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="餐厅" align="center" prop="diningRoom" width="150">
        <template slot-scope="scope">
          <div class="image-cell">
            <el-image 
              v-if="scope.row.diningRoom" 
              :src="formatImageUrl(scope.row.diningRoom)" 
              :preview-src-list="[formatImageUrl(scope.row.diningRoom)]"
              fit="cover"
              class="room-image"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>图片加载失败</span>
              </div>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无该图</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="厨房" align="center" prop="kitchen" width="150">
        <template slot-scope="scope">
          <div class="image-cell">
            <el-image 
              v-if="scope.row.kitchen" 
              :src="formatImageUrl(scope.row.kitchen)" 
              :preview-src-list="[formatImageUrl(scope.row.kitchen)]"
              fit="cover"
              class="room-image"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>图片加载失败</span>
              </div>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无该图</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="卫生间" align="center" prop="bathroom" width="150">
        <template slot-scope="scope">
          <div class="image-cell">
            <el-image 
              v-if="scope.row.bathroom" 
              :src="formatImageUrl(scope.row.bathroom)" 
              :preview-src-list="[formatImageUrl(scope.row.bathroom)]"
              fit="cover"
              class="room-image"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>图片加载失败</span>
              </div>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无该图</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="阳台" align="center" prop="balcony" width="150">
        <template slot-scope="scope">
          <div class="image-cell">
            <el-image 
              v-if="scope.row.balcony" 
              :src="formatImageUrl(scope.row.balcony)" 
              :preview-src-list="[formatImageUrl(scope.row.balcony)]"
              fit="cover"
              class="room-image"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
                <span>图片加载失败</span>
              </div>
            </el-image>
            <div v-else class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无该图</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="上传人员" align="center" prop="uploadPersonnel" width="120" />
      <el-table-column label="上传小区" align="center" prop="uploadCommunity" width="150" />
      <el-table-column label="上传时间" align="center" prop="uploadTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewImages(scope.row)"
          >查看图片</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['sc:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['sc:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      :auto-scroll="false"
      @pagination="getList"
    />

    <!-- 添加或修改房屋信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        
        <!-- 图片信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <i class="el-icon-picture-outline"></i>
            <span>房间图片信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客厅" prop="livingRoom">
                <ImageUpload 
                  v-model="form.livingRoom"
                  :limit="1"
                  :fileSize="10"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="餐厅" prop="diningRoom">
                <ImageUpload 
                  v-model="form.diningRoom"
                  :limit="1"
                  :fileSize="10"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="厨房" prop="kitchen">
                <ImageUpload 
                  v-model="form.kitchen"
                  :limit="1"
                  :fileSize="10"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="卫生间" prop="bathroom">
                <ImageUpload 
                  v-model="form.bathroom"
                  :limit="1"
                  :fileSize="10"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="阳台" prop="balcony">
                <ImageUpload 
                  v-model="form.balcony"
                  :limit="1"
                  :fileSize="10"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 基本信息分组 -->
        <div class="form-section">
          <div class="section-title">
            <i class="el-icon-user"></i>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="上传人员" prop="uploadPersonnel">
                <el-input v-model="form.uploadPersonnel" placeholder="请输入上传人员" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上传小区" prop="uploadCommunity">
                <el-input v-model="form.uploadCommunity" placeholder="请输入上传小区" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="上传时间" prop="uploadTime">
                <el-date-picker
                  v-model="form.uploadTime"
                  type="datetime"
                  placeholder="选择上传时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="房间图片预览" :visible.sync="imagePreviewOpen" width="80%" append-to-body>
      <div class="image-preview-container">
        <div class="preview-header">
          <h3>{{ currentViewRow.uploadCommunity }} - {{ currentViewRow.uploadPersonnel }}</h3>
          <p>上传时间：{{ parseTime(currentViewRow.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</p>
        </div>
        <div class="room-images-grid">
          <div 
            v-for="room in roomTypes" 
            :key="room.field" 
            class="room-preview-item"
          >
            <div class="room-preview-header">
              <span class="room-emoji">{{ room.emoji }}</span>
              <span class="room-name">{{ room.name }}</span>
            </div>
            <div class="room-preview-image">
              <el-image 
                v-if="currentViewRow[room.field]" 
                :src="formatImageUrl(currentViewRow[room.field])" 
                :preview-src-list="getAllImages()"
                fit="cover"
                class="preview-room-image"
              >
                <div slot="error" class="preview-image-error">
                  <i class="el-icon-picture-outline"></i>
                  <span>图片加载失败</span>
                </div>
              </el-image>
              <div v-else class="preview-no-image">
                <i class="el-icon-picture-outline"></i>
                <span>暂无{{ room.name }}图片</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, delInfo, updateInfo } from "@/api/sc/info";
import ImageUpload from "@/components/ImageUpload";

export default {
  name: "UploadInformation",
  components: {
    ImageUpload
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 房屋信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示图片预览对话框
      imagePreviewOpen: false,
      // 当前查看的行数据
      currentViewRow: {},
      // 房间类型配置
      roomTypes: [
        { field: 'livingRoom', name: '客厅', emoji: '🛋️' },
        { field: 'diningRoom', name: '餐厅', emoji: '🍽️' },
        { field: 'kitchen', name: '厨房', emoji: '👩‍🍳' },
        { field: 'bathroom', name: '卫生间', emoji: '🚿' },
        { field: 'balcony', name: '阳台', emoji: '🌿' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        uploadCommunity: null,
        uploadPersonnel: null,
        timeRange: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        uploadPersonnel: [
          { required: true, message: "上传人员不能为空", trigger: "blur" }
        ],
        uploadCommunity: [
          { required: true, message: "上传小区不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询房屋信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        livingRoom: null,
        diningRoom: null,
        kitchen: null,
        bathroom: null,
        balcony: null,
        uploadPersonnel: null,
        uploadCommunity: null,
        uploadTime: null
      };
      this.resetForm("form");
    },
         /** 搜索按钮操作 */
     handleQuery() {
       this.queryParams.pageNum = 1;
       this.getList();
     },
         /** 重置按钮操作 */
     resetQuery() {
       this.resetForm("queryForm");
       this.queryParams.timeRange = null;
       this.handleQuery();
     },


    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      let infoData;
      if (row && row.id) {
        // 从表格行点击编辑
        infoData = row;
      } else {
        // 从工具栏点击编辑（需要先选中行）
        if (this.ids.length === 1) {
          infoData = this.infoList.find(item => item.id === this.ids[0]);
        }
      }
      
      if (infoData) {
        this.form = Object.assign({}, infoData);
        this.open = true;
        this.title = "修改房屋信息";
      } else {
        this.$modal.msgError("请选择要修改的数据");
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除房屋信息编号为"' + ids + '"的数据项？').then(function() {
        return delInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 查看图片操作 */
    handleViewImages(row) {
      this.currentViewRow = Object.assign({}, row);
      this.imagePreviewOpen = true;
    },

    /** 获取所有有效图片URL列表 */
    getAllImages() {
      const images = [];
      this.roomTypes.forEach(room => {
        if (this.currentViewRow[room.field]) {
          images.push(this.formatImageUrl(this.currentViewRow[room.field]));
        }
      });
      return images;
    },

    /** 统一处理图片URL格式 */
    formatImageUrl(url) {
      if (!url) return '';
      
      // 如果已经是完整的URL（包含http或https），直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      
      // 如果是相对路径，添加基础URL前缀  || 'http://localhost:9898'
      const baseUrl = process.env.VUE_APP_BASE_API;
      return baseUrl + url;
    }

  }
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 15px;
}

/* 图片单元格容器 */
.image-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80px;
}

/* 房间图片样式 */
.room-image {
  width: 100px;
  height: 70px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: transform 0.2s;
}

.room-image:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 无图片状态 */
.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 70px;
  background-color: #f5f7fa;
  border: 1px dashed #d3d4d6;
  border-radius: 6px;
  color: #909399;
  font-size: 12px;
}

.no-image i {
  font-size: 20px;
  margin-bottom: 4px;
  color: #c0c4cc;
}

.no-image span {
  font-size: 11px;
  line-height: 1;
}

/* 图片加载错误状态 */
.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.image-slot i {
  font-size: 20px;
  margin-bottom: 4px;
  color: #c0c4cc;
}

.image-slot span {
  font-size: 11px;
  line-height: 1;
}

/* 表格行高度调整 */
.el-table /deep/ .el-table__body-wrapper .el-table__row {
  height: 90px;
}

.el-table /deep/ .el-table__body-wrapper .el-table__row td {
  padding: 8px 0;
  vertical-align: middle;
}

/* 图片上传组件的自定义样式 */
::v-deep .component-upload-image .el-upload--picture-card {
  width: 120px;
  height: 80px;
  line-height: 80px;
}

::v-deep .component-upload-image .el-upload-list--picture-card .el-upload-list__item {
  width: 120px;
  height: 80px;
}

/* 图片预览对话框样式 */
.image-preview-container {
  padding: 20px;
}

.preview-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.preview-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.room-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.room-preview-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  transition: transform 0.2s, box-shadow 0.2s;
}

.room-preview-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.room-preview-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  gap: 8px;
}

.room-emoji {
  font-size: 20px;
}

.room-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.room-preview-image {
  display: flex;
  justify-content: center;
}

.preview-room-image {
  width: 160px;
  height: 120px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: transform 0.2s;
}

.preview-room-image:hover {
  transform: scale(1.02);
}

.preview-no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160px;
  height: 120px;
  background-color: #f5f7fa;
  border: 2px dashed #d3d4d6;
  border-radius: 8px;
  color: #909399;
  font-size: 14px;
}

.preview-no-image i {
  font-size: 32px;
  margin-bottom: 8px;
  color: #c0c4cc;
}

.preview-no-image span {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

.preview-image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.preview-image-error i {
  font-size: 32px;
  margin-bottom: 8px;
  color: #c0c4cc;
}

.preview-image-error span {
  font-size: 12px;
  text-align: center;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding: 10px 0;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409eff;
}
</style>

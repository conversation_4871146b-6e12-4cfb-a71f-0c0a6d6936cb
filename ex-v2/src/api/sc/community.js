import request from '@/utils/request'

// 查询社区列表
export function listCommunity(query) {
  return request({
    url: '/sc/community/list',
    method: 'get',
    params: query
  })
}

// 新增社区
export function addCommunity(data) {
  return request({
    url: '/sc/community/add',
    method: 'post',
    data: data
  })
}

// 修改社区
export function updateCommunity(data) {
  return request({
    url: '/sc/community/edit',
    method: 'post',
    data: data
  })
}

// 删除社区
export function delCommunity(ids) {
  return request({
    url: '/sc/community/remove',
    method: 'post',
    data: 'ids=' + ids,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 导出社区
export function exportCommunity(query) {
  return request({
    url: '/sc/community/export',
    method: 'post',
    params: query
  })
} 
import request from '@/utils/request'

// 查询房屋信息列表
export function listInfo(query) {
  return request({
    url: '/sc/info/list',
    method: 'get',
    params: query
  })
}

// 查询房屋信息详细
export function getInfo(id) {
  return request({
    url: '/sc/info/' + id,
    method: 'get'
  })
}

// 新增房屋信息
export function addInfo(data) {
  return request({
    url: '/sc/info/add',
    method: 'post',
    data: data
  })
}

// 修改房屋信息
export function updateInfo(data) {
  return request({
    url: '/sc/info/edit',
    method: 'post',
    data: data
  })
}

// 删除房屋信息
export function delInfo(ids) {
  return request({
    url: '/sc/info/remove',
    method: 'post',
    data: 'ids=' + ids,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 导出房屋信息
export function exportInfo(query) {
  return request({
    url: '/sc/info/export',
    method: 'post',
    params: query
  })
} 